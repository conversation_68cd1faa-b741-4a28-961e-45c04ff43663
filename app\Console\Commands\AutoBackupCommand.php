<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\BackupLog;
use App\Models\BackupSetting;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use App\Notifications\BackupEmailNotification;

class AutoBackupCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:auto
                            {--force : Force backup regardless of schedule}
                            {--test : Run in test mode}
                            {--email : Send email notification}
                            {--compress : Compress backup file}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'تشغيل النسخ الاحتياطي التلقائي';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->info('🚀 بدء فحص النسخ الاحتياطي التلقائي...');
            Log::info('AutoBackupCommand: Starting automatic backup check');

            // التحقق من إعدادات النسخ الاحتياطي التلقائي
            $settings = BackupSetting::first();

            if (!$settings) {
                $this->error('❌ لم يتم العثور على إعدادات النسخ الاحتياطي');
                Log::error('AutoBackupCommand: No backup settings found');
                return 1;
            }

            if (!$settings->auto_backup_enabled && !$this->option('force')) {
                $this->warn('⚠️ النسخ الاحتياطي التلقائي معطل');
                Log::info('AutoBackupCommand: Auto backup is disabled');
                return 0;
            }

            // التحقق من الوقت المحدد للنسخ الاحتياطي
            $shouldCreateBackup = $this->option('force') || $this->shouldRunBackupNow($settings);

            if ($shouldCreateBackup) {
                $this->info('✅ بدء إنشاء النسخة الاحتياطية...');

                // إنشاء نسخة احتياطية تلقائية
                $backup = BackupLog::create([
                    'filename' => '',
                    'path' => '',
                    'type' => $this->option('force') ? 'manual' : 'automatic',
                    'status' => 'processing',
                    'started_at' => now(),
                    'created_by' => 1, // النظام
                ]);

                Log::info("AutoBackupCommand: Created backup log with ID: {$backup->id}");

                // تشغيل النسخ الاحتياطي
                $this->processBackup($backup, $settings);

                $this->info('🎉 تم إنشاء النسخة الاحتياطية بنجاح!');
                Log::info('AutoBackupCommand: Automatic backup completed successfully');

                return 0;
            } else {
                $this->info('⏰ ليس الوقت المحدد للنسخ الاحتياطي');
                Log::info('AutoBackupCommand: Not the scheduled time for backup');
                return 0;
            }

        } catch (\Exception $e) {
            $this->error('❌ فشل في النسخ الاحتياطي: ' . $e->getMessage());
            Log::error('AutoBackupCommand: Error - ' . $e->getMessage());
            Log::error('AutoBackupCommand: Stack trace - ' . $e->getTraceAsString());
            return 1;
        }
    }

    /**
     * تحديد ما إذا كان يجب تشغيل النسخ الاحتياطي الآن
     */
    private function shouldRunBackupNow($settings)
    {
        $currentTime = now();
        $currentHour = $currentTime->format('H');
        $currentMinute = $currentTime->format('i');

        // استخراج الوقت المحدد من الإعدادات
        $backupTime = $settings->backup_time;
        if (!$backupTime) {
            Log::warning('AutoBackupCommand: No backup time set in settings');
            return false;
        }

        // تحويل الوقت المحدد إلى ساعة ودقيقة
        $timeParts = explode(':', $backupTime);
        $scheduledHour = (int) $timeParts[0];
        $scheduledMinute = isset($timeParts[1]) ? (int) $timeParts[1] : 0;

        // التحقق من التكرار
        $frequency = $settings->backup_frequency ?? 'daily';

        // التحقق من الوقت الحالي مقابل الوقت المحدد (مع هامش دقيقة واحدة)
        $isCorrectTime = ($currentHour == $scheduledHour) &&
                        (abs($currentMinute - $scheduledMinute) <= 1);

        if (!$isCorrectTime) {
            Log::info("AutoBackupCommand: Current time {$currentHour}:{$currentMinute} doesn't match scheduled time {$scheduledHour}:{$scheduledMinute}");
            return false;
        }

        // التحقق من آخر نسخة احتياطية لتجنب التكرار
        $lastBackup = BackupLog::where('status', 'completed')
                              ->where('type', 'automatic')
                              ->orderBy('created_at', 'desc')
                              ->first();

        if ($lastBackup) {
            $hoursSinceLastBackup = $lastBackup->created_at->diffInHours($currentTime);

            switch ($frequency) {
                case 'daily':
                    if ($hoursSinceLastBackup < 23) {
                        Log::info("AutoBackupCommand: Daily backup already done today ({$hoursSinceLastBackup} hours ago)");
                        return false;
                    }
                    break;

                case 'weekly':
                    if ($hoursSinceLastBackup < (7 * 24 - 1)) {
                        Log::info("AutoBackupCommand: Weekly backup already done this week ({$hoursSinceLastBackup} hours ago)");
                        return false;
                    }
                    break;

                case 'monthly':
                    if ($hoursSinceLastBackup < (30 * 24 - 1)) {
                        Log::info("AutoBackupCommand: Monthly backup already done this month ({$hoursSinceLastBackup} hours ago)");
                        return false;
                    }
                    break;
            }
        }

        Log::info("AutoBackupCommand: Backup should run now - Time: {$currentHour}:{$currentMinute}, Frequency: {$frequency}");
        return true;
    }

    /**
     * معالجة النسخ الاحتياطي المحسن
     */
    private function processBackup($backup, $settings)
    {
        $progressBar = $this->output->createProgressBar(6);
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %message%');

        try {
            // الخطوة 1: إعداد المسارات
            $progressBar->setMessage('إعداد المسارات والملفات...');
            $progressBar->advance();

            $timestamp = now()->format('Y-m-d_H-i-s');
            $dbName = config('database.connections.mysql.database');
            $filename = "backup_{$dbName}_{$timestamp}.sql";
            $backupPath = storage_path('app/backups');

            // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
            if (!file_exists($backupPath)) {
                mkdir($backupPath, 0755, true);
                $this->info("\n📁 تم إنشاء مجلد النسخ الاحتياطية: {$backupPath}");
            }

            $fullPath = $backupPath . '/' . $filename;

            // الخطوة 2: التحقق من اتصال قاعدة البيانات
            $progressBar->setMessage('التحقق من اتصال قاعدة البيانات...');
            $progressBar->advance();

            $this->validateDatabaseConnection();

            // الخطوة 3: إنشاء النسخة الاحتياطية
            $progressBar->setMessage('إنشاء النسخة الاحتياطية...');
            $progressBar->advance();

            $this->createDatabaseBackup($fullPath);

            // الخطوة 4: التحقق من صحة الملف
            $progressBar->setMessage('التحقق من صحة الملف...');
            $progressBar->advance();

            $fileSize = $this->validateBackupFile($fullPath);

            // الخطوة 5: ضغط الملف (اختياري)
            $progressBar->setMessage('ضغط الملف...');
            $progressBar->advance();

            if ($settings->compress_backups || $this->option('compress')) {
                $compressedPath = $this->compressBackup($fullPath);
                if ($compressedPath) {
                    unlink($fullPath); // حذف الملف غير المضغوط
                    $fullPath = $compressedPath;
                    $filename = basename($compressedPath);
                    $fileSize = filesize($fullPath);
                    $this->info("\n🗜️ تم ضغط النسخة الاحتياطية");
                }
            }

            // الخطوة 6: تحديث السجل وإرسال الإشعارات
            $progressBar->setMessage('إنهاء العملية...');
            $progressBar->advance();

            // تحديث سجل النسخة الاحتياطية
            $backup->update([
                'filename' => $filename,
                'path' => $fullPath,
                'status' => 'completed',
                'completed_at' => now(),
                'size' => $fileSize,
            ]);

            $progressBar->finish();
            $this->info("\n✅ تم إنشاء النسخة الاحتياطية بنجاح!");
            $this->info("📄 اسم الملف: {$filename}");
            $this->info("📊 حجم الملف: " . $this->formatBytes($fileSize));

            Log::info("AutoBackupCommand: Backup completed successfully. File: {$filename}, Size: {$fileSize} bytes");

            // إرسال إشعارات النجاح
            $this->sendNotifications($backup, 'success', $settings);

            // تنظيف النسخ القديمة
            $this->cleanupOldBackups($settings);

        } catch (\Exception $e) {
            $progressBar->finish();

            $backup->update([
                'status' => 'failed',
                'completed_at' => now(),
                'error_message' => $e->getMessage(),
            ]);

            $this->error("\n❌ فشل في النسخ الاحتياطي: " . $e->getMessage());
            Log::error("AutoBackupCommand: Backup failed - " . $e->getMessage());

            // إرسال إشعارات الفشل
            $this->sendNotifications($backup, 'failed', $settings);

            throw $e;
        }
    }

    /**
     * إرسال إيميل النسخة الاحتياطية
     */
    private function sendBackupEmail($backup, $status)
    {
        try {
            // الحصول على إعدادات النسخ الاحتياطي
            $settings = BackupSetting::first();

            if (!$settings || !$settings->email_notifications) {
                Log::info('AutoBackupCommand: Email notifications are disabled');
                return;
            }

            // الحصول على قائمة الإيميلات
            $emails = [];

            // إضافة الإيميل المحدد أولاً
            $emails[] = '<EMAIL>';

            if ($settings->notification_emails) {
                $settingsEmails = array_map('trim', explode(',', $settings->notification_emails));
                $settingsEmails = array_filter($settingsEmails, function($email) {
                    return filter_var($email, FILTER_VALIDATE_EMAIL);
                });
                $emails = array_merge($emails, $settingsEmails);
            }

            // إضافة إيميلات المديرين
            $adminUsers = User::whereIn('role', ['super', 'admin'])->get();
            foreach ($adminUsers as $user) {
                if ($user->email && filter_var($user->email, FILTER_VALIDATE_EMAIL)) {
                    $emails[] = $user->email;
                }
            }

            // إزالة الإيميلات المكررة
            $emails = array_unique($emails);

            if (empty($emails)) {
                Log::warning('AutoBackupCommand: No valid email addresses found for backup notifications');
                return;
            }

            Log::info('AutoBackupCommand: Sending backup email to: ' . implode(', ', $emails));

            // إرسال الإيميل لكل عنوان
            foreach ($emails as $email) {
                try {
                    Notification::route('mail', $email)
                              ->notify(new BackupEmailNotification($backup, $status));

                    Log::info("AutoBackupCommand: Backup email sent successfully to: {$email}");
                } catch (\Exception $e) {
                    Log::error("AutoBackupCommand: Failed to send backup email to {$email}: " . $e->getMessage());
                }
            }

        } catch (\Exception $e) {
            Log::error('AutoBackupCommand: Error sending backup email: ' . $e->getMessage());
        }
    }
}

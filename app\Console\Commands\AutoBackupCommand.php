<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\BackupLog;
use App\Models\BackupSetting;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use App\Notifications\BackupEmailNotification;

class AutoBackupCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:auto';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'تشغيل النسخ الاحتياطي التلقائي';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            Log::info('AutoBackupCommand: Starting automatic backup check');

            // التحقق من إعدادات النسخ الاحتياطي التلقائي
            $settings = BackupSetting::first();

            if (!$settings || !$settings->auto_backup_enabled) {
                Log::info('AutoBackupCommand: Auto backup is disabled');
                return;
            }

            // التحقق من الوقت المحدد للنسخ الاحتياطي
            $shouldCreateBackup = $this->shouldRunBackupNow($settings);

            if ($shouldCreateBackup) {
                // إنشاء نسخة احتياطية تلقائية
                $backup = BackupLog::create([
                    'filename' => '',
                    'path' => '',
                    'type' => 'automatic',
                    'status' => 'processing',
                    'started_at' => now(),
                    'created_by' => 1, // النظام
                ]);

                Log::info("AutoBackupCommand: Created backup log with ID: {$backup->id}");

                // تشغيل النسخ الاحتياطي
                $this->processBackup($backup);

                Log::info('AutoBackupCommand: Automatic backup completed successfully');
                $this->info('تم إنشاء النسخة الاحتياطية التلقائية بنجاح');
            } else {
                Log::info('AutoBackupCommand: Not the scheduled time for backup');
            }

        } catch (\Exception $e) {
            Log::error('AutoBackupCommand: Error - ' . $e->getMessage());
            Log::error('AutoBackupCommand: Stack trace - ' . $e->getTraceAsString());
            $this->error('فشل في النسخ الاحتياطي التلقائي: ' . $e->getMessage());
        }
    }

    /**
     * تحديد ما إذا كان يجب تشغيل النسخ الاحتياطي الآن
     */
    private function shouldRunBackupNow($settings)
    {
        $currentTime = now();
        $currentHour = $currentTime->format('H');
        $currentMinute = $currentTime->format('i');

        // استخراج الوقت المحدد من الإعدادات
        $backupTime = $settings->backup_time;
        if (!$backupTime) {
            Log::warning('AutoBackupCommand: No backup time set in settings');
            return false;
        }

        // تحويل الوقت المحدد إلى ساعة ودقيقة
        $timeParts = explode(':', $backupTime);
        $scheduledHour = (int) $timeParts[0];
        $scheduledMinute = isset($timeParts[1]) ? (int) $timeParts[1] : 0;

        // التحقق من التكرار
        $frequency = $settings->backup_frequency ?? 'daily';

        // التحقق من الوقت الحالي مقابل الوقت المحدد (مع هامش دقيقة واحدة)
        $isCorrectTime = ($currentHour == $scheduledHour) &&
                        (abs($currentMinute - $scheduledMinute) <= 1);

        if (!$isCorrectTime) {
            Log::info("AutoBackupCommand: Current time {$currentHour}:{$currentMinute} doesn't match scheduled time {$scheduledHour}:{$scheduledMinute}");
            return false;
        }

        // التحقق من آخر نسخة احتياطية لتجنب التكرار
        $lastBackup = BackupLog::where('status', 'completed')
                              ->where('type', 'automatic')
                              ->orderBy('created_at', 'desc')
                              ->first();

        if ($lastBackup) {
            $hoursSinceLastBackup = $lastBackup->created_at->diffInHours($currentTime);

            switch ($frequency) {
                case 'daily':
                    if ($hoursSinceLastBackup < 23) {
                        Log::info("AutoBackupCommand: Daily backup already done today ({$hoursSinceLastBackup} hours ago)");
                        return false;
                    }
                    break;

                case 'weekly':
                    if ($hoursSinceLastBackup < (7 * 24 - 1)) {
                        Log::info("AutoBackupCommand: Weekly backup already done this week ({$hoursSinceLastBackup} hours ago)");
                        return false;
                    }
                    break;

                case 'monthly':
                    if ($hoursSinceLastBackup < (30 * 24 - 1)) {
                        Log::info("AutoBackupCommand: Monthly backup already done this month ({$hoursSinceLastBackup} hours ago)");
                        return false;
                    }
                    break;
            }
        }

        Log::info("AutoBackupCommand: Backup should run now - Time: {$currentHour}:{$currentMinute}, Frequency: {$frequency}");
        return true;
    }

    /**
     * معالجة النسخ الاحتياطي
     */
    private function processBackup($backup)
    {
        try {
            $timestamp = now()->format('Y-m-d_H-i-s');
            $filename = "backup_auto_{$timestamp}.sql";
            $backupPath = storage_path('app/backups');

            // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
            if (!file_exists($backupPath)) {
                mkdir($backupPath, 0755, true);
            }

            $fullPath = $backupPath . '/' . $filename;

            // الحصول على إعدادات قاعدة البيانات
            $host = config('database.connections.mysql.host');
            $database = config('database.connections.mysql.database');
            $username = config('database.connections.mysql.username');
            $password = config('database.connections.mysql.password');

            // تنفيذ أمر mysqldump مع معالجة محسنة
            if (empty($password)) {
                $command = "mysqldump -h{$host} -u{$username} --single-transaction --routines --triggers {$database} > \"{$fullPath}\"";
            } else {
                $command = "mysqldump -h{$host} -u{$username} -p{$password} --single-transaction --routines --triggers {$database} > \"{$fullPath}\"";
            }

            Log::info('AutoBackupCommand: Executing mysqldump command');

            $returnCode = 0;
            $output = [];
            exec($command . ' 2>&1', $output, $returnCode);

            Log::info("AutoBackupCommand: Command return code: {$returnCode}");
            if (!empty($output)) {
                Log::info("AutoBackupCommand: Command output: " . implode("\n", $output));
            }

            if ($returnCode !== 0) {
                throw new \Exception('فشل في تنفيذ أمر mysqldump: ' . implode("\n", $output));
            }

            if (!file_exists($fullPath)) {
                throw new \Exception('فشل في إنشاء ملف النسخة الاحتياطية - الملف غير موجود');
            }

            $fileSize = filesize($fullPath);
            if ($fileSize === 0) {
                throw new \Exception('فشل في إنشاء ملف النسخة الاحتياطية - الملف فارغ');
            }

            // تحديث سجل النسخة الاحتياطية
            $backup->update([
                'filename' => $filename,
                'path' => $fullPath,
                'status' => 'completed',
                'completed_at' => now(),
                'size' => $fileSize,
            ]);

            Log::info("AutoBackupCommand: Backup completed successfully. File size: {$fileSize} bytes");

            // إرسال إيميل النجاح
            $this->sendBackupEmail($backup, 'success');

            // تنظيف النسخ القديمة
            $settings = BackupSetting::first();
            $maxBackups = $settings ? $settings->max_backups_to_keep : 7;
            BackupLog::cleanupOldBackups($maxBackups);

        } catch (\Exception $e) {
            $backup->update([
                'status' => 'failed',
                'completed_at' => now(),
                'error_message' => $e->getMessage(),
            ]);

            // إرسال إيميل الفشل
            $this->sendBackupEmail($backup, 'failed');

            throw $e;
        }
    }

    /**
     * إرسال إيميل النسخة الاحتياطية
     */
    private function sendBackupEmail($backup, $status)
    {
        try {
            // الحصول على إعدادات النسخ الاحتياطي
            $settings = BackupSetting::first();

            if (!$settings || !$settings->email_notifications) {
                Log::info('AutoBackupCommand: Email notifications are disabled');
                return;
            }

            // الحصول على قائمة الإيميلات
            $emails = [];

            // إضافة الإيميل المحدد أولاً
            $emails[] = '<EMAIL>';

            if ($settings->notification_emails) {
                $settingsEmails = array_map('trim', explode(',', $settings->notification_emails));
                $settingsEmails = array_filter($settingsEmails, function($email) {
                    return filter_var($email, FILTER_VALIDATE_EMAIL);
                });
                $emails = array_merge($emails, $settingsEmails);
            }

            // إضافة إيميلات المديرين
            $adminUsers = User::whereIn('role', ['super', 'admin'])->get();
            foreach ($adminUsers as $user) {
                if ($user->email && filter_var($user->email, FILTER_VALIDATE_EMAIL)) {
                    $emails[] = $user->email;
                }
            }

            // إزالة الإيميلات المكررة
            $emails = array_unique($emails);

            if (empty($emails)) {
                Log::warning('AutoBackupCommand: No valid email addresses found for backup notifications');
                return;
            }

            Log::info('AutoBackupCommand: Sending backup email to: ' . implode(', ', $emails));

            // إرسال الإيميل لكل عنوان
            foreach ($emails as $email) {
                try {
                    Notification::route('mail', $email)
                              ->notify(new BackupEmailNotification($backup, $status));

                    Log::info("AutoBackupCommand: Backup email sent successfully to: {$email}");
                } catch (\Exception $e) {
                    Log::error("AutoBackupCommand: Failed to send backup email to {$email}: " . $e->getMessage());
                }
            }

        } catch (\Exception $e) {
            Log::error('AutoBackupCommand: Error sending backup email: ' . $e->getMessage());
        }
    }
}

<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;
use Illuminate\Support\Facades\Log;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// جدولة النسخ الاحتياطي التلقائي - ديناميكي حسب إعدادات قاعدة البيانات
Schedule::call(function () {
    try {
        $settings = \App\Models\BackupSetting::first();

        if (!$settings || !$settings->auto_backup_enabled) {
            Log::info('Scheduled backup: Auto backup is disabled');
            return;
        }

        // التحقق من الوقت المحدد مع دقة أفضل
        $currentTime = now();
        $currentHour = (int) $currentTime->format('H');
        $currentMinute = (int) $currentTime->format('i');

        $backupTime = $settings->backup_time;
        if (!$backupTime) {
            Log::debug('Scheduled backup: No backup time set, using default 02:00');
            $backupTime = '02:00:00';
        }

        // استخراج الوقت المحدد
        $timeParts = explode(':', $backupTime);
        $scheduledHour = (int) $timeParts[0];
        $scheduledMinute = isset($timeParts[1]) ? (int) $timeParts[1] : 0;

        Log::debug("Scheduled backup check: Current time {$currentHour}:{$currentMinute}, Scheduled time {$scheduledHour}:{$scheduledMinute}");

        // التحقق من الوقت (مع هامش دقيقة واحدة)
        $isCorrectTime = ($currentHour == $scheduledHour) &&
                        (abs($currentMinute - $scheduledMinute) <= 1);

        if (!$isCorrectTime) {
            return; // ليس الوقت المناسب
        }

        // التحقق من التكرار
        $frequency = $settings->backup_frequency ?? 'daily';
        $shouldRun = false;

        if ($frequency === 'daily') {
            $shouldRun = true;
        } elseif ($frequency === 'weekly') {
            $shouldRun = $currentTime->dayOfWeek === 1; // الاثنين
        } elseif ($frequency === 'monthly') {
            $shouldRun = $currentTime->day === 1; // أول يوم في الشهر
        }

        if ($shouldRun) {
            Log::info("Scheduled backup: Starting automatic backup at {$currentHour}:{$currentMinute} (frequency: {$frequency})");
            Artisan::call('backup:auto');
            Log::info('Scheduled backup: Backup command executed successfully');
        } else {
            Log::info("Scheduled backup: Correct time but wrong day for {$frequency} backup");
        }

    } catch (\Exception $e) {
        Log::error('Scheduled backup error: ' . $e->getMessage());
    }
})->everyMinute()
  ->name('dynamic-backup-scheduler')
  ->withoutOverlapping();

// جدولة النسخ الاحتياطي للاختبار - كل 10 دقائق في البيئة المحلية
Schedule::command('backup:auto')
    ->everyTenMinutes()
    ->withoutOverlapping()
    ->name('test-backup-scheduler')
    ->environments(['local'])
    ->onSuccess(function () {
        Log::info('Test backup completed successfully');
    })
    ->onFailure(function () {
        Log::error('Test backup failed');
    });

// جدولة تنظيف النسخ القديمة - أسبوعياً
Schedule::call(function () {
    \App\Models\BackupLog::cleanupOldBackups(7);
    Log::info('Old backups cleaned up');
})->weekly()
  ->name('cleanup-old-backups');

// جدولة التذكيرات (إذا كانت مطلوبة)
// Schedule::command('reminders:send')
//     ->everyMinute()
//     ->withoutOverlapping()
//     ->name('send-reminders-scheduler');

// تشخيص النظام يومياً
Schedule::command('backup:diagnose')
    ->dailyAt('01:00')
    ->name('daily-backup-diagnosis')
    ->onSuccess(function () {
        Log::info('Daily backup system diagnosis completed');
    });

/**
 * نظام الإشعارات البسيط الجديد
 * Simple Notification System - من الصفر تماماً
 * 
 * المميزات:
 * - إشعار واحد فقط بعد إضافة عميل جديد
 * - لا يختفي تلقائياً (persistent)
 * - يعمل على جميع الأجهزة والمتصفحات
 * - تصميم بسيط وواضح
 */

class SimpleNotificationSystem {
    constructor() {
        this.isInitialized = false;
        this.currentNotification = null;
        this.settings = this.loadSettings();
        this.storageKey = 'simple_notification_data';
        
        console.log('🔔 تهيئة نظام الإشعارات البسيط الجديد');
        this.init();
    }

    /**
     * تهيئة النظام
     */
    init() {
        if (this.isInitialized) {
            console.log('⚠️ النظام مهيأ مسبقاً');
            return;
        }

        // تحميل الإعدادات من الخادم
        this.loadUserSettings();

        // إضافة CSS للنظام
        this.addSystemStyles();

        // فحص وجود إشعار محفوظ
        this.checkForSavedNotification();

        // مراقبة إضافة العملاء الجدد
        this.monitorNewClients();

        // إنشاء البالون بجانب الجرس
        this.createBellBalloon();

        // تهيئة الإشعارات الفورية (Real-time)
        this.initializeRealTimeNotifications();

        this.isInitialized = true;
        console.log('✅ تم تهيئة نظام الإشعارات البسيط بنجاح');
    }

    /**
     * تحميل إعدادات المستخدم من الخادم
     */
    async loadUserSettings() {
        try {
            const response = await fetch('/api/notification-settings');
            if (response.ok) {
                const data = await response.json();
                this.settings = { ...this.settings, ...data.settings };
                console.log('✅ تم تحميل إعدادات المستخدم');
            }
        } catch (error) {
            console.log('⚠️ استخدام الإعدادات الافتراضية');
        }
    }

    /**
     * تحميل الإعدادات الافتراضية
     */
    loadSettings() {
        return {
            color: '#667eea',           // اللون الافتراضي (بنفسجي حديث)
            position: 'bottom-left',    // الموقع الافتراضي (أسفل اليسار)
            size: 'medium',             // الحجم الافتراضي
            customText: 'تم إضافة عميل جديد بنجاح!', // النص المخصص
            enabled: true               // تفعيل النظام
        };
    }

    /**
     * إضافة CSS للنظام
     */
    addSystemStyles() {
        if (document.getElementById('simple-notification-styles')) {
            return;
        }

        const style = document.createElement('style');
        style.id = 'simple-notification-styles';
        style.textContent = `
            /* نظام الإشعارات البسيط */
            .simple-notification {
                position: fixed;
                z-index: 10000;
                background: linear-gradient(135deg, var(--notification-color, #667eea), var(--notification-color-dark, #764ba2));
                color: white;
                border-radius: 16px;
                box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                direction: rtl;
                text-align: right;
                border: 1px solid rgba(255, 255, 255, 0.2);
                backdrop-filter: blur(15px);
                animation: slideInLeft 0.5s cubic-bezier(0.4, 0, 0.2, 1);
                user-select: none;
                cursor: pointer;
                overflow: hidden;
                position: relative;
            }

            /* أحجام الإشعارات */
            .simple-notification.size-small {
                padding: 12px 16px;
                font-size: 13px;
                max-width: 280px;
                min-width: 200px;
            }

            .simple-notification.size-medium {
                padding: 16px 20px;
                font-size: 14px;
                max-width: 350px;
                min-width: 250px;
            }

            .simple-notification.size-large {
                padding: 20px 24px;
                font-size: 16px;
                max-width: 420px;
                min-width: 300px;
            }

            /* مواقع الإشعارات المحسنة للوضوح */
            .simple-notification.position-top-right {
                top: 100px;
                right: 30px;
                z-index: 999999;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            }

            .simple-notification.position-top-left {
                top: 100px;
                left: 30px;
                z-index: 999999;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            }

            .simple-notification.position-bottom-right {
                bottom: 30px;
                right: 30px;
                z-index: 999999;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            }

            .simple-notification.position-bottom-left {
                bottom: 30px;
                left: 30px;
                z-index: 999999;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            }

            .simple-notification.position-bottom-center {
                bottom: 30px;
                left: 50%;
                transform: translateX(-50%);
                z-index: 999999;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            }

            .simple-notification.position-center {
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 999999;
                box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
            }

            /* موقع خاص للإشعارات المهمة - أعلى الوسط */
            .simple-notification.position-top-center {
                top: 100px;
                left: 50%;
                transform: translateX(-50%);
                z-index: 999999;
                box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
                min-width: 400px;
            }

            /* محتوى الإشعار */
            .notification-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 8px;
            }

            .notification-icon {
                background: rgba(255, 255, 255, 0.2);
                border-radius: 50%;
                width: 32px;
                height: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                margin-left: 10px;
            }

            .notification-title {
                font-weight: bold;
                font-size: 1.1em;
                flex: 1;
            }

            .notification-close {
                background: rgba(255, 255, 255, 0.2);
                border: none;
                border-radius: 50%;
                width: 28px;
                height: 28px;
                color: white;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                font-weight: bold;
                transition: all 0.2s ease;
            }

            .notification-close:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: scale(1.1);
            }

            .notification-body {
                margin-bottom: 8px;
            }

            .notification-client-name {
                font-weight: bold;
                font-size: 1.05em;
                margin-bottom: 4px;
                background: rgba(255, 255, 255, 0.15);
                padding: 4px 8px;
                border-radius: 6px;
                display: inline-block;
            }

            .notification-datetime {
                font-size: 0.85em;
                opacity: 0.9;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            /* البالون بجانب الجرس */
            .bell-balloon {
                position: fixed;
                top: 90px;
                right: 15px;
                background: linear-gradient(135deg, #ff6b6b, #ee5a24);
                color: white;
                padding: 10px 15px;
                border-radius: 25px;
                font-size: 13px;
                font-weight: bold;
                z-index: 10001;
                box-shadow: 0 6px 20px rgba(255, 107, 107, 0.5);
                animation: balloonPulse 2s ease-in-out infinite;
                cursor: pointer;
                direction: rtl;
                text-align: right;
                border: 2px solid rgba(255, 255, 255, 0.3);
                backdrop-filter: blur(10px);
                display: none;
                min-width: 120px;
                max-width: 200px;
            }

            .bell-balloon:hover {
                transform: scale(1.05);
                box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
            }

            /* الأنيميشن المحسن */
            @keyframes slideInLeft {
                from {
                    opacity: 0;
                    transform: translateX(-100%) scale(0.8);
                }
                50% {
                    opacity: 0.8;
                    transform: translateX(10px) scale(1.05);
                }
                to {
                    opacity: 1;
                    transform: translateX(0) scale(1);
                }
            }

            @keyframes slideOutLeft {
                from {
                    opacity: 1;
                    transform: translateX(0) scale(1);
                }
                to {
                    opacity: 0;
                    transform: translateX(-100%) scale(0.8);
                }
            }

            /* تأثير لمعان */
            .simple-notification::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
                animation: shine 3s infinite;
                z-index: 1;
            }

            @keyframes shine {
                0% { left: -100%; }
                100% { left: 100%; }
            }

            /* تأثير hover */
            .simple-notification:hover {
                transform: scale(1.02);
                box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2), 0 6px 16px rgba(0, 0, 0, 0.15);
                animation: pulse 0.6s ease-in-out;
            }

            @keyframes pulse {
                0% { transform: scale(1.02); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1.02); }
            }

            /* تأثيرات حركة إضافية */
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            @keyframes bounceIn {
                0% {
                    opacity: 0;
                    transform: scale(0.3) translateX(-50px);
                }
                50% {
                    opacity: 1;
                    transform: scale(1.05) translateX(5px);
                }
                70% {
                    transform: scale(0.9) translateX(-2px);
                }
                100% {
                    opacity: 1;
                    transform: scale(1) translateX(0);
                }
            }

            @keyframes zoomIn {
                from {
                    opacity: 0;
                    transform: scale(0.5) translateX(-20px);
                }
                to {
                    opacity: 1;
                    transform: scale(1) translateX(0);
                }
            }

            @keyframes slideInRight {
                from {
                    opacity: 0;
                    transform: translateX(100%) scale(0.8);
                }
                50% {
                    opacity: 0.8;
                    transform: translateX(-10px) scale(1.05);
                }
                to {
                    opacity: 1;
                    transform: translateX(0) scale(1);
                }
            }

            @keyframes flipIn {
                from {
                    opacity: 0;
                    transform: perspective(400px) rotateY(90deg) translateX(-50px);
                }
                40% {
                    transform: perspective(400px) rotateY(-20deg) translateX(10px);
                }
                60% {
                    transform: perspective(400px) rotateY(10deg) translateX(-5px);
                }
                to {
                    opacity: 1;
                    transform: perspective(400px) rotateY(0deg) translateX(0);
                }
            }

            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(100%) scale(0.8);
                }
                50% {
                    opacity: 0.8;
                    transform: translateY(-10px) scale(1.05);
                }
                to {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }

            @keyframes slideInDown {
                from {
                    opacity: 0;
                    transform: translateY(-100%) scale(0.8);
                }
                50% {
                    opacity: 0.8;
                    transform: translateY(10px) scale(1.05);
                }
                to {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }

            @keyframes rotateIn {
                from {
                    opacity: 0;
                    transform: rotate(-200deg) scale(0.5) translateX(-50px);
                }
                to {
                    opacity: 1;
                    transform: rotate(0deg) scale(1) translateX(0);
                }
            }

            @keyframes rollIn {
                from {
                    opacity: 0;
                    transform: translateX(-100%) rotate(-120deg);
                }
                to {
                    opacity: 1;
                    transform: translateX(0) rotate(0deg);
                }
            }

            /* تأثيرات الخروج */
            @keyframes slideOutLeft {
                from {
                    opacity: 1;
                    transform: translateX(0) scale(1);
                }
                to {
                    opacity: 0;
                    transform: translateX(-100%) scale(0.8);
                }
            }

            @keyframes slideOutRight {
                from {
                    opacity: 1;
                    transform: translateX(0) scale(1);
                }
                to {
                    opacity: 0;
                    transform: translateX(100%) scale(0.8);
                }
            }

            @keyframes slideOutUp {
                from {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
                to {
                    opacity: 0;
                    transform: translateY(-100%) scale(0.8);
                }
            }

            @keyframes slideOutDown {
                from {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
                to {
                    opacity: 0;
                    transform: translateY(100%) scale(0.8);
                }
            }

            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }

            @keyframes bounceOut {
                20% {
                    transform: scale3d(0.9, 0.9, 0.9);
                }
                50%, 55% {
                    opacity: 1;
                    transform: scale3d(1.1, 1.1, 1.1);
                }
                to {
                    opacity: 0;
                    transform: scale3d(0.3, 0.3, 0.3);
                }
            }

            @keyframes zoomOut {
                from {
                    opacity: 1;
                    transform: scale(1);
                }
                50% {
                    opacity: 0.5;
                    transform: scale(1.1);
                }
                to {
                    opacity: 0;
                    transform: scale(0.3);
                }
            }

            @keyframes flipOut {
                from {
                    opacity: 1;
                    transform: perspective(400px) rotateY(0deg);
                }
                to {
                    opacity: 0;
                    transform: perspective(400px) rotateY(90deg);
                }
            }

            /* تأثير النبض */
            .pulse-effect {
                animation: pulse-glow 2s infinite;
            }

            @keyframes pulse-glow {
                0% {
                    box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
                }
                50% {
                    box-shadow: 0 0 20px rgba(102, 126, 234, 0.6), 0 0 30px rgba(102, 126, 234, 0.4);
                }
                100% {
                    box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
                }
            }

            @keyframes balloonPulse {
                0%, 100% {
                    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
                    transform: scale(1);
                }
                50% {
                    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
                    transform: scale(1.02);
                }
            }

            /* التصميم المتجاوب */
            @media (max-width: 768px) {
                .simple-notification {
                    max-width: 90vw !important;
                    min-width: 280px !important;
                    margin: 10px !important;
                }

                .simple-notification.position-top-right {
                    top: 60px !important;
                    right: 10px !important;
                    left: auto !important;
                }

                .simple-notification.position-top-left {
                    top: 60px !important;
                    left: 10px !important;
                    right: auto !important;
                }

                .simple-notification.position-bottom-right {
                    bottom: 10px !important;
                    right: 10px !important;
                    left: auto !important;
                }

                .simple-notification.position-bottom-left {
                    bottom: 10px !important;
                    left: 10px !important;
                    right: auto !important;
                }

                .simple-notification.position-bottom-center {
                    bottom: 10px !important;
                    left: 50% !important;
                    right: auto !important;
                    transform: translateX(-50%) !important;
                }

                .simple-notification.position-center {
                    top: 50% !important;
                    left: 50% !important;
                    right: auto !important;
                    transform: translate(-50%, -50%) !important;
                    max-width: 85vw !important;
                }

                .bell-balloon {
                    top: 80px;
                    right: 10px;
                    left: 10px;
                    font-size: 12px;
                    padding: 8px 12px;
                    min-width: auto;
                    max-width: calc(100vw - 20px);
                    text-align: center;
                }
            }

            @media (max-width: 480px) {
                .simple-notification.size-large {
                    padding: 16px 20px;
                    font-size: 14px;
                }

                .simple-notification.size-medium {
                    padding: 14px 18px;
                    font-size: 13px;
                }

                .simple-notification.size-small {
                    padding: 12px 16px;
                    font-size: 12px;
                }
            }
        `;

        document.head.appendChild(style);
        console.log('✅ تم إضافة CSS للنظام');
    }

    /**
     * فحص وجود إشعار محفوظ
     */
    checkForSavedNotification() {
        try {
            const savedData = localStorage.getItem(this.storageKey);
            if (savedData) {
                const notificationData = JSON.parse(savedData);
                
                // التحقق من أن الإشعار لم يتم إغلاقه
                if (!notificationData.dismissed) {
                    console.log('🔄 استعادة إشعار محفوظ');
                    this.showNotification(notificationData.clientName, notificationData.timestamp, false);
                }
            }
        } catch (error) {
            console.error('❌ خطأ في فحص الإشعار المحفوظ:', error);
        }
    }

    /**
     * مراقبة إضافة العملاء الجدد
     */
    monitorNewClients() {
        // مراقبة session flash messages
        if (typeof window.clientAdded !== 'undefined' && window.clientAdded) {
            console.log('🆕 تم اكتشاف عميل جديد من session');
            this.showNotification(window.clientAdded.name, new Date().toISOString());
        }

        // مراقبة localStorage للعملاء الجدد
        window.addEventListener('storage', (e) => {
            if (e.key === 'new_client_added') {
                const clientData = JSON.parse(e.newValue);
                console.log('🆕 تم اكتشاف عميل جديد من localStorage');
                this.showNotification(clientData.name, clientData.timestamp);
            }
        });

        console.log('👀 تم تفعيل مراقبة العملاء الجدد');
    }

    /**
     * إظهار الإشعار
     */
    showNotification(clientName, timestamp = null, saveToStorage = true) {
        if (!this.settings.enabled) {
            console.log('❌ نظام الإشعارات معطل');
            return;
        }

        // إزالة الإشعار الحالي إن وجد
        this.hideCurrentNotification();

        const notificationData = {
            clientName: clientName,
            timestamp: timestamp || new Date().toISOString(),
            dismissed: false
        };

        // حفظ في localStorage
        if (saveToStorage) {
            localStorage.setItem(this.storageKey, JSON.stringify(notificationData));
        }

        // إنشاء الإشعار
        const notification = this.createNotificationElement(notificationData);
        document.body.appendChild(notification);

        this.currentNotification = notification;

        // إظهار البالون بجانب الجرس
        this.showBellBalloon();

        // تحديث عداد الجرس
        this.updateBellCounter();

        console.log(`✅ تم إظهار إشعار للعميل: ${clientName}`);
    }

    /**
     * إنشاء عنصر الإشعار
     */
    createNotificationElement(data) {
        const notification = document.createElement('div');
        notification.className = `simple-notification position-${this.settings.position} size-${this.settings.size}`;

        // تطبيق اللون المخصص
        notification.style.setProperty('--notification-color', this.settings.color);

        // تطبيق إعدادات الشكل المخصصة
        this.applyCustomStyles(notification);
        notification.style.setProperty('--notification-color-dark', this.darkenColor(this.settings.color, 20));

        const date = new Date(data.timestamp);
        const formattedDate = date.toLocaleDateString('ar-SA');
        const formattedTime = date.toLocaleTimeString('ar-SA');

        notification.innerHTML = `
            <div class="notification-header">
                <div class="notification-icon">🎉</div>
                <div class="notification-title">${this.settings.customText}</div>
                <button class="notification-close" onclick="simpleNotificationSystem.hideNotification()" title="إغلاق">×</button>
            </div>
            <div class="notification-body">
                <div class="notification-client-name">👤 ${data.clientName}</div>
                <div class="notification-datetime">
                    <span>📅 ${formattedDate}</span>
                    <span>🕐 ${formattedTime}</span>
                </div>
            </div>
        `;

        return notification;
    }

    /**
     * إخفاء الإشعار الحالي
     */
    hideCurrentNotification() {
        if (this.currentNotification) {
            this.currentNotification.remove();
            this.currentNotification = null;
        }
    }

    /**
     * إغلاق الإشعار نهائياً
     */
    hideNotification() {
        this.hideCurrentNotification();
        
        // وضع علامة الإغلاق في localStorage
        try {
            const savedData = localStorage.getItem(this.storageKey);
            if (savedData) {
                const notificationData = JSON.parse(savedData);
                notificationData.dismissed = true;
                localStorage.setItem(this.storageKey, JSON.stringify(notificationData));
            }
        } catch (error) {
            console.error('❌ خطأ في حفظ حالة الإغلاق:', error);
        }

        console.log('✅ تم إغلاق الإشعار');
    }

    /**
     * إنشاء البالون بجانب الجرس
     */
    createBellBalloon() {
        if (document.getElementById('bell-balloon')) {
            return;
        }

        const balloon = document.createElement('div');
        balloon.id = 'bell-balloon';
        balloon.className = 'bell-balloon';
        balloon.innerHTML = `
            <div style="display: flex; align-items: center; gap: 5px;">
                <span>🔔</span>
                <span>عميل جديد!</span>
                <button onclick="simpleNotificationSystem.hideBellBalloon()" 
                        style="background: rgba(255,255,255,0.2); border: none; border-radius: 50%; width: 16px; height: 16px; color: white; cursor: pointer; margin-right: 5px; font-size: 10px;">×</button>
            </div>
        `;

        document.body.appendChild(balloon);
        console.log('✅ تم إنشاء البالون بجانب الجرس');
    }

    /**
     * إظهار البالون بجانب الجرس
     */
    showBellBalloon() {
        const balloon = document.getElementById('bell-balloon');
        if (balloon) {
            balloon.style.display = 'block';
            console.log('🎈 تم إظهار البالون بجانب الجرس');
        }
    }

    /**
     * إخفاء البالون بجانب الجرس
     */
    hideBellBalloon() {
        const balloon = document.getElementById('bell-balloon');
        if (balloon) {
            balloon.style.display = 'none';
            console.log('🎈 تم إخفاء البالون بجانب الجرس');
        }
    }

    /**
     * تحديث عداد الجرس
     */
    updateBellCounter() {
        try {
            const countSelectors = [
                '.notification-count',
                '.badge',
                '.notification-badge',
                '[data-notification-count]',
                '.bell-count',
                '.navbar .badge',
                '.nav-link .badge'
            ];
            
            let countElement = null;
            
            for (const selector of countSelectors) {
                countElement = document.querySelector(selector);
                if (countElement) {
                    break;
                }
            }
            
            if (countElement) {
                const currentCount = parseInt(countElement.textContent) || 0;
                const newCount = currentCount + 1;
                countElement.textContent = newCount;
                
                // تأثير بصري
                countElement.style.animation = 'none';
                countElement.offsetHeight; // trigger reflow
                countElement.style.animation = 'bellCounterUpdate 0.6s ease-out';
                
                console.log(`🔔 تم تحديث عداد الجرس إلى: ${newCount}`);
            }
        } catch (error) {
            console.error('❌ خطأ في تحديث عداد الجرس:', error);
        }
    }

    /**
     * تحديث الإعدادات
     */
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        
        // إعادة تطبيق الإعدادات على الإشعار الحالي
        if (this.currentNotification) {
            this.currentNotification.className = `simple-notification position-${this.settings.position} size-${this.settings.size}`;
            this.currentNotification.style.setProperty('--notification-color', this.settings.color);
            this.currentNotification.style.setProperty('--notification-color-dark', this.darkenColor(this.settings.color, 20));
        }
        
        console.log('⚙️ تم تحديث إعدادات النظام');
    }

    /**
     * تغميق اللون
     */
    darkenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) - amt;
        const G = (num >> 8 & 0x00FF) - amt;
        const B = (num & 0x0000FF) - amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }

    /**
     * تهيئة الإشعارات الفورية (Real-time)
     */
    initializeRealTimeNotifications() {
        try {
            // التحقق من وجود Pusher
            if (typeof Pusher === 'undefined') {
                console.warn('⚠️ Pusher غير متاح - سيتم استخدام نظام الاستطلاع البديل');
                this.initializeFallbackPolling();
                return;
            }

            // إعداد Pusher
            const pusherConfig = window.pusherConfig || {
                key: 'local',
                cluster: 'mt1',
                encrypted: true
            };

            // مهلة زمنية للاتصال
            const connectionTimeout = setTimeout(() => {
                if (this.connectionStatus !== 'connected') {
                    console.warn('⏰ انتهت مهلة الاتصال بـ Pusher - التبديل للنظام البديل');
                    if (this.pusher) {
                        this.pusher.disconnect();
                    }
                    this.initializeFallbackPolling();
                }
            }, 5000);

            this.pusher = new Pusher(pusherConfig.key, {
                cluster: pusherConfig.cluster,
                encrypted: pusherConfig.encrypted,
                host: pusherConfig.host || '127.0.0.1',
                port: pusherConfig.port || 6001,
                scheme: pusherConfig.scheme || 'http',
                disableStats: true,
                enabledTransports: ['ws', 'wss'],
                wsHost: pusherConfig.host || '127.0.0.1',
                wsPort: pusherConfig.port || 6001,
                wssPort: pusherConfig.port || 6001,
                forceTLS: false
            });

            // الاشتراك في قناة الإشعارات
            this.channel = this.pusher.subscribe('client-notifications');

            // الاستماع لأحداث العملاء الجدد
            this.channel.bind('new-client-added', (data) => {
                console.log('🔔 تم استقبال إشعار فوري:', data);
                this.handleRealTimeNotification(data);
            });

            // مراقبة حالة الاتصال
            this.pusher.connection.bind('connected', () => {
                console.log('✅ تم الاتصال بخدمة الإشعارات الفورية عبر Pusher');
                this.connectionStatus = 'connected';
                this.connectionType = 'Pusher WebSocket';
                clearTimeout(connectionTimeout);
            });

            this.pusher.connection.bind('disconnected', () => {
                console.log('❌ انقطع الاتصال بخدمة الإشعارات الفورية');
                this.connectionStatus = 'disconnected';
                // التبديل للنظام البديل عند انقطاع الاتصال
                setTimeout(() => {
                    this.initializeFallbackPolling();
                }, 2000);
            });

            this.pusher.connection.bind('error', (error) => {
                console.error('❌ خطأ في الاتصال بـ Pusher:', error);
                this.connectionStatus = 'error';
                clearTimeout(connectionTimeout);
                // التبديل للنظام البديل عند حدوث خطأ
                setTimeout(() => {
                    this.initializeFallbackPolling();
                }, 1000);
            });

            console.log('🚀 جاري تهيئة نظام الإشعارات الفورية عبر Pusher...');

        } catch (error) {
            console.error('❌ خطأ في تهيئة الإشعارات الفورية:', error);
            this.connectionStatus = 'error';
            // التبديل للنظام البديل عند حدوث خطأ
            this.initializeFallbackPolling();
        }
    }

    /**
     * تهيئة نظام الاستطلاع البديل (Fallback Polling)
     */
    initializeFallbackPolling() {
        try {
            // إيقاف أي استطلاع سابق
            if (this.pollingInterval) {
                clearInterval(this.pollingInterval);
            }

            console.log('🔄 تهيئة نظام الاستطلاع البديل للإشعارات...');

            this.connectionStatus = 'connected';
            this.connectionType = 'HTTP Polling';
            this.lastNotificationCheck = Date.now();

            // استطلاع دوري كل 10 ثوانٍ
            this.pollingInterval = setInterval(() => {
                this.checkForNewNotifications();
            }, 10000);

            // فحص فوري أول
            setTimeout(() => {
                this.checkForNewNotifications();
            }, 1000);

            console.log('✅ تم تفعيل نظام الاستطلاع البديل');

        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام الاستطلاع البديل:', error);
            this.connectionStatus = 'error';
        }
    }

    /**
     * فحص الإشعارات الجديدة عبر HTTP
     */
    async checkForNewNotifications() {
        try {
            const response = await fetch('/api/notifications/recent', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                }
            });

            if (response.ok) {
                const data = await response.json();

                if (data.success && data.notifications) {
                    // فلترة الإشعارات الجديدة فقط
                    const newNotifications = data.notifications.filter(notification => {
                        const notificationTime = new Date(notification.created_at).getTime();
                        return notificationTime > this.lastNotificationCheck;
                    });

                    // معالجة الإشعارات الجديدة
                    newNotifications.forEach(notification => {
                        console.log('🔔 تم استقبال إشعار عبر الاستطلاع:', notification);
                        this.handleRealTimeNotification(notification);
                    });

                    // تحديث وقت آخر فحص
                    this.lastNotificationCheck = Date.now();
                }
            }

        } catch (error) {
            console.error('❌ خطأ في فحص الإشعارات الجديدة:', error);
        }
    }

    /**
     * معالجة الإشعار الفوري
     */
    handleRealTimeNotification(data) {
        try {
            // التحقق من صحة البيانات
            if (!data.client || !data.client.name) {
                console.error('❌ بيانات الإشعار الفوري غير صحيحة:', data);
                return;
            }

            const clientName = data.client.name;
            const clientId = data.client.id;
            const timestamp = data.timestamp || new Date().toISOString();

            console.log(`🆕 عميل جديد تم إضافته: ${clientName}`);

            // إظهار الإشعار
            this.showNotification(clientName, timestamp, true);

            // إظهار البالون
            this.showBellBalloon();

            // تحديث عداد الجرس
            this.updateBellCounter();

            // إشعار صوتي (اختياري)
            this.playNotificationSound();

            // إشعار سطح المكتب (اختياري)
            this.showDesktopNotification(clientName, clientId);

        } catch (error) {
            console.error('❌ خطأ في معالجة الإشعار الفوري:', error);
        }
    }

    /**
     * تشغيل صوت الإشعار
     */
    playNotificationSound() {
        try {
            // إنشاء صوت بسيط باستخدام Web Audio API
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);

        } catch (error) {
            console.log('🔇 لا يمكن تشغيل الصوت:', error.message);
        }
    }

    /**
     * إظهار إشعار سطح المكتب
     */
    showDesktopNotification(clientName, clientId) {
        try {
            // التحقق من دعم الإشعارات
            if (!('Notification' in window)) {
                return;
            }

            // طلب الإذن إذا لم يكن ممنوحاً
            if (Notification.permission === 'default') {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        this.createDesktopNotification(clientName, clientId);
                    }
                });
            } else if (Notification.permission === 'granted') {
                this.createDesktopNotification(clientName, clientId);
            }

        } catch (error) {
            console.log('🖥️ لا يمكن إظهار إشعار سطح المكتب:', error.message);
        }
    }

    /**
     * إنشاء إشعار سطح المكتب
     */
    createDesktopNotification(clientName, clientId) {
        try {
            const notification = new Notification('عميل جديد تم إضافته!', {
                body: `تم إضافة العميل: ${clientName}`,
                icon: '/favicon.ico',
                badge: '/favicon.ico',
                tag: 'new-client-' + clientId,
                requireInteraction: true,
                actions: [
                    {
                        action: 'view',
                        title: 'عرض العميل'
                    },
                    {
                        action: 'dismiss',
                        title: 'إغلاق'
                    }
                ]
            });

            // إغلاق تلقائي بعد 10 ثوانٍ
            setTimeout(() => {
                notification.close();
            }, 10000);

            // معالجة النقر على الإشعار
            notification.onclick = function() {
                window.focus();
                if (clientId) {
                    window.open(`/estpsdetalsnoe/${clientId}`, '_blank');
                }
                notification.close();
            };

        } catch (error) {
            console.error('❌ خطأ في إنشاء إشعار سطح المكتب:', error);
        }
    }

    /**
     * الحصول على حالة الاتصال
     */
    getConnectionStatus() {
        return this.connectionStatus || 'unknown';
    }

    /**
     * الحصول على نوع الاتصال
     */
    getConnectionType() {
        return this.connectionType || 'WebSocket/Pusher';
    }

    /**
     * إعادة الاتصال بخدمة الإشعارات
     */
    reconnect() {
        try {
            // إيقاف الاتصالات الحالية
            if (this.pusher) {
                this.pusher.disconnect();
            }

            if (this.pollingInterval) {
                clearInterval(this.pollingInterval);
            }

            this.connectionStatus = 'unknown';
            console.log('🔄 جاري إعادة الاتصال بخدمة الإشعارات...');

            setTimeout(() => {
                this.initializeRealTimeNotifications();
            }, 1000);

        } catch (error) {
            console.error('❌ خطأ في إعادة الاتصال:', error);
        }
    }

    /**
     * قطع الاتصال
     */
    disconnect() {
        try {
            if (this.pusher) {
                this.pusher.disconnect();
                console.log('🔌 تم قطع الاتصال بـ Pusher');
            }

            if (this.pollingInterval) {
                clearInterval(this.pollingInterval);
                console.log('🔌 تم إيقاف نظام الاستطلاع البديل');
            }

            this.connectionStatus = 'disconnected';
            console.log('🔌 تم قطع الاتصال بخدمة الإشعارات الفورية');

        } catch (error) {
            console.error('❌ خطأ في قطع الاتصال:', error);
        }
    }

    /**
     * اختبار النظام
     */
    test() {
        this.showNotification('عميل تجريبي ' + Date.now());
    }

    /**
     * اختبار الإشعارات الفورية
     */
    testRealTime() {
        // محاكاة إشعار فوري
        const mockData = {
            client: {
                id: 999,
                name: 'عميل تجريبي فوري ' + new Date().toLocaleTimeString('ar-SA'),
                phone: '123456789'
            },
            timestamp: new Date().toISOString(),
            notification: {
                type: 'new_client_added',
                message: 'اختبار الإشعارات الفورية'
            }
        };

        this.handleRealTimeNotification(mockData);
    }

    /**
     * تطبيق إعدادات الشكل المخصصة
     */
    applyCustomStyles(notification) {
        if (!this.settings) return;

        // إعدادات الخطوط
        if (this.settings.fontFamily) {
            notification.style.fontFamily = this.settings.fontFamily;
        }
        if (this.settings.fontSize) {
            notification.style.fontSize = this.settings.fontSize;
        }
        if (this.settings.fontWeight) {
            notification.style.fontWeight = this.settings.fontWeight;
        }

        // إعدادات الألوان والتدرجات المتقدمة
        if (this.settings.primaryColor && this.settings.secondaryColor) {
            let background;
            const angle = this.settings.gradientAngle || 135;

            switch (this.settings.gradientType) {
                case 'radial':
                    background = `radial-gradient(circle, ${this.settings.primaryColor}, ${this.settings.secondaryColor})`;
                    break;
                case 'conic':
                    background = `conic-gradient(from ${angle}deg, ${this.settings.primaryColor}, ${this.settings.secondaryColor}, ${this.settings.primaryColor})`;
                    break;
                case 'solid':
                    background = this.settings.primaryColor;
                    break;
                default:
                    background = `linear-gradient(${angle}deg, ${this.settings.primaryColor}, ${this.settings.secondaryColor})`;
            }
            notification.style.background = background;
        }

        if (this.settings.textColor) {
            notification.style.color = this.settings.textColor;
        }

        // إعدادات الشكل والحدود
        if (this.settings.borderRadius !== undefined) {
            notification.style.borderRadius = `${this.settings.borderRadius}px`;
        }

        if (this.settings.borderWidth !== undefined && this.settings.borderColor && this.settings.borderOpacity !== undefined) {
            const borderColor = this.hexToRgba(this.settings.borderColor, this.settings.borderOpacity);
            notification.style.border = `${this.settings.borderWidth}px solid ${borderColor}`;
        }

        // إعدادات الشفافية
        if (this.settings.backgroundOpacity !== undefined) {
            notification.style.opacity = this.settings.backgroundOpacity;
        }

        // إعدادات الظلال المتقدمة
        if (this.settings.enableShadow === false) {
            notification.style.boxShadow = 'none';
        } else {
            const intensity = this.settings.shadowIntensity || 0.15;
            const spread = this.settings.shadowSpread || 20;
            const blur = this.settings.shadowBlur || 40;
            const shadowColor = this.settings.shadowColor || '#000000';
            const shadowRgba = this.hexToRgba(shadowColor, intensity);

            notification.style.boxShadow = `0 ${spread/2}px ${blur}px ${shadowRgba}`;
        }

        // تأثير التوهج المتقدم
        if (this.settings.enableGlow === false) {
            notification.style.filter = 'none';
        } else {
            let filters = [];

            if (this.settings.enableBlur !== false) {
                filters.push('blur(0.5px)');
            }

            const glowIntensity = this.settings.glowIntensity || 0.3;
            const glowSize = this.settings.glowSize || 10;
            const glowColor = this.settings.primaryColor || '#667eea';

            filters.push(`drop-shadow(0 0 ${glowSize}px ${this.hexToRgba(glowColor, glowIntensity)})`);
            notification.style.filter = filters.join(' ');
        }

        // تأثير اللمعان
        if (this.settings.enableShine === false) {
            notification.style.setProperty('--shine-display', 'none');
        }

        // تأثير النبض
        if (this.settings.enablePulse === true) {
            notification.classList.add('pulse-effect');
        }

        // إعدادات الحركة المتقدمة
        if (this.settings.animationType && this.settings.animationSpeed && this.settings.timingFunction) {
            const delay = this.settings.animationDelay || 0;
            const iteration = this.settings.animationIteration || 1;

            notification.style.animation = `${this.settings.animationType} ${this.settings.animationSpeed}s ${this.settings.timingFunction} ${delay}s ${iteration}`;
        }

        // مدة العرض مع تأثير الخروج
        if (this.settings.displayDuration) {
            setTimeout(() => {
                this.hideNotificationWithAnimation(notification);
            }, this.settings.displayDuration * 1000);
        }
    }

    /**
     * إخفاء الإشعار مع تأثير الخروج
     */
    hideNotificationWithAnimation(notification) {
        if (this.settings.exitAnimation && this.settings.exitSpeed) {
            notification.style.animation = `${this.settings.exitAnimation} ${this.settings.exitSpeed}s ${this.settings.timingFunction || 'ease'}`;

            setTimeout(() => {
                this.hideNotification(notification);
            }, this.settings.exitSpeed * 1000);
        } else {
            this.hideNotification(notification);
        }
    }

    /**
     * تحويل HEX إلى RGBA
     */
    hexToRgba(hex, alpha) {
        const r = parseInt(hex.slice(1, 3), 16);
        const g = parseInt(hex.slice(3, 5), 16);
        const b = parseInt(hex.slice(5, 7), 16);
        return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    window.simpleNotificationSystem = new SimpleNotificationSystem();
});

// إضافة CSS للعداد
const bellCounterStyle = document.createElement('style');
bellCounterStyle.textContent = `
    @keyframes bellCounterUpdate {
        0% { transform: scale(1); }
        50% { transform: scale(1.3); background: #ff6b6b; }
        100% { transform: scale(1); }
    }
`;
document.head.appendChild(bellCounterStyle);
